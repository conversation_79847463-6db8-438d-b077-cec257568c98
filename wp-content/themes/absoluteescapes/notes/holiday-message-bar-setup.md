# Holiday Message Bar - Setup Guide

## Overview
The Holiday Message Bar is an optional notification bar that can appear at the top of individual holiday pages, positioned before the primary navigation to avoid positioning conflicts.

## Features
- **Per-holiday control**: Each holiday can have its own unique message and on/off setting
- **Optional linking**: Messages can be made clickable with custom URLs
- **Link target options**: Links can open in the same window or new tab
- **Color customization**: Choose from predefined background and text color combinations
- **Responsive design**: Optimized for all screen sizes
- **Accessibility**: Proper ARIA attributes and semantic HTML

## ACF Field Configuration

### Field Group: "Holiday Message Bar"
**Location:** Post Type → Holiday

### Fields Structure:
```
Holiday Message Bar (Group)
├── Show Message Bar (True/False)
├── Message (Textarea) - conditional on Show Message Bar = Yes
├── Link URL (URL) - conditional on Show Message Bar = Yes
├── Link Target (Select) - conditional on Show Message Bar = Yes AND Link URL is not empty
├── Background Color (Select) - conditional on Show Message Bar = Yes
└── Text Color (Select) - conditional on Show Message Bar = Yes
```

### Field Options:

#### Background Color Options:
- **Teal** (default) - Brand teal color
- **Blue** - Brand blue-grey color
- **Green** - Success green
- **Orange** - Warning orange
- **Red** - Error/alert red
- **Grey** - Neutral grey

#### Text Color Options:
- **White** (default) - For dark backgrounds
- **Black** - For light backgrounds
- **Dark Grey** - For medium backgrounds

#### Link Target Options:
- **Same window** (default) - Opens in current tab
- **New window/tab** - Opens in new tab with proper security attributes

## Usage Instructions

### For Content Editors:

1. **Edit a Holiday Post**
   - Go to WordPress Admin → Holidays → Edit [Holiday Name]

2. **Configure Message Bar**
   - Scroll to the "Holiday Message Bar" section
   - Toggle "Show Message Bar" to "Yes"
   - Enter your message in the "Message" field
   - (Optional) Add a "Link URL" to make the bar clickable
   - Choose appropriate background and text colors
   - Save the post

3. **Best Practices**
   - Keep messages concise and actionable
   - Use contrasting colors for readability
   - Test links before publishing
   - Consider mobile users when writing messages

### Message Examples:
- "Special offer: Book by March 31st for 15% off this holiday!"
- "New departure dates added for summer 2024"
- "This holiday is filling up fast - only 3 spaces remaining"
- "COVID-19 travel updates available - click for latest information"

## Technical Implementation

### Files Created/Modified:
- `lib/acf-json/group_holiday_message_bar.json` - ACF field group definition
- `lib/components/holiday-message-bar.php` - Frontend component template
- `assets/styles/components/_holiday-message-bar.scss` - Component styles
- `single-holiday.php` - Template integration (appears before hero panel)
- `functions.php` - Body class functionality

### CSS Classes:
- `.holiday-message-bar` - Main container
- `.holiday-message-bar--bg-{color}` - Background color modifiers
- `.holiday-message-bar--text-{color}` - Text color modifiers
- `.holiday-message-bar--clickable` - Applied when message is linkable
- `.has-holiday-message-bar` - Body class when message bar is active

### Positioning:
The message bar appears at the top of the page content, positioned below the AITO review bar and above the hero panel. It uses a 44px top margin to ensure it appears below the AITO bar (which is positioned at bottom: -44px from the masthead). When active, it pushes the hero panel down naturally without affecting any navigation positioning.

## Troubleshooting

### Message Bar Not Showing:
1. Check that "Show Message Bar" is set to "Yes"
2. Ensure the "Message" field has content
3. Verify the holiday post is published
4. Clear any caching plugins

### Styling Issues:
1. Run `npx gulp styles` from the theme directory to recompile CSS
2. Check browser developer tools for CSS conflicts
3. Ensure proper color contrast for accessibility

### Link Not Working:
1. Verify the URL format is correct (include http:// or https://)
2. Check that the URL field is not empty
3. Test the link target setting

## Browser Support
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Internet Explorer 11+ (with graceful degradation)
- Mobile browsers (iOS Safari, Chrome Mobile)

## Accessibility Features
- Semantic HTML structure
- Proper ARIA attributes for external links
- Keyboard navigation support
- Screen reader compatible
- Color contrast compliance (when using recommended color combinations)
